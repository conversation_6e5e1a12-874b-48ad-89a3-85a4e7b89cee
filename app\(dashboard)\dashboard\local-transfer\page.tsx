"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DashboardBreadcrumb } from "@/components/ui/dashboard-breadcrumb";
import { Input } from "@/components/ui/input";
import { TransactionPreviewModal } from "@/components/ui/transaction-preview-modal";
import { motion } from "framer-motion";
import { ArrowLeftRight, DollarSign, Eye, EyeOff, User } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function LocalTransfer() {
   const [formData, setFormData] = useState({
      recipientName: "",
      accountNumber: "",
      bankName: "",
      amount: "",
      description: "",
   });
   const [showBalance, setShowBalance] = useState(true);
   const [showPreviewModal, setShowPreviewModal] = useState(false);
   const [isProcessing, setIsProcessing] = useState(false);

   // Mock account balance
   const accountBalance = 12847.52;

   // Authentication is handled by the dashboard layout

   const handleInputChange = (
      e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
   ) => {
      const { name, value } = e.target;
      setFormData((prev) => ({
         ...prev,
         [name]: value,
      }));
   };

   const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      setShowPreviewModal(true);
   };

   // const handleConfirmTransaction = async (pin: string) => {
   const handleConfirmTransaction = async () => {
      setIsProcessing(true);
      try {
         // Simulate transaction processing
         await new Promise((resolve) => setTimeout(resolve, 2000));

         toast.success(
            `Local transfer of $${formData.amount} to ${formData.recipientName} completed successfully!`
         );
         setFormData({
            recipientName: "",
            accountNumber: "",
            bankName: "",
            amount: "",
            description: "",
         });
         setShowPreviewModal(false);
      } catch (error) {
         console.error("Transaction error:", error);
         toast.error("Transaction failed. Please try again.");
      } finally {
         setIsProcessing(false);
      }
   };

   return (
      <div>
         {/* Breadcrumbs */}
         <DashboardBreadcrumb
            items={[
               { label: "Transfers", href: "/dashboard" },
               { label: "Local" },
            ]}
         />

         {/* Main Content */}
         <div className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               className="max-w-2xl mx-auto"
            >
               <div className="text-center mb-8">
                  <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                     <ArrowLeftRight className="w-8 h-8 text-primary" />
                  </div>
                  <h1 className="text-3xl font-bold text-foreground mb-2">
                     Local Transfer
                  </h1>
                  <p className="text-muted-foreground">
                     Send money to other accounts within the United States.
                     Transfers are processed instantly and free of charge.
                  </p>
               </div>

               {/* Account Balance Card */}
               <div className="bank-card p-6 mb-6">
                  <div className="flex items-center justify-between">
                     <div>
                        <h2 className="text-lg font-semibold text-foreground mb-1">
                           Available Balance
                        </h2>
                        <p className="text-3xl font-bold text-foreground">
                           {showBalance
                              ? `$${accountBalance.toLocaleString("en-US", {
                                   minimumFractionDigits: 2,
                                })}`
                              : "••••••"}
                        </p>
                     </div>
                     <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowBalance(!showBalance)}
                        className="text-muted-foreground hover:text-foreground"
                     >
                        {showBalance ? (
                           <EyeOff className="h-5 w-5" />
                        ) : (
                           <Eye className="h-5 w-5" />
                        )}
                     </Button>
                  </div>
               </div>

               <div className="bank-card p-6">
                  <form onSubmit={handleSubmit} className="space-y-6">
                     {/* Recipient Information */}
                     <div className="space-y-4">
                        <h2 className="text-lg font-semibold text-foreground flex items-center gap-2">
                           <User className="w-5 h-5" />
                           Recipient Information
                        </h2>

                        <Input
                           label="Recipient Name"
                           name="recipientName"
                           value={formData.recipientName}
                           onChange={handleInputChange}
                           placeholder="Enter recipient's full name"
                           required
                        />

                        <Input
                           label="Account Number"
                           name="accountNumber"
                           value={formData.accountNumber}
                           onChange={handleInputChange}
                           placeholder="Enter recipient's account number"
                           required
                        />

                        <Input
                           label="Bank Name"
                           name="bankName"
                           value={formData.bankName}
                           onChange={handleInputChange}
                           placeholder="Enter recipient's bank name"
                           required
                        />
                     </div>

                     {/* Transfer Details */}
                     <div className="space-y-4">
                        <h2 className="text-lg font-semibold text-foreground flex items-center gap-2">
                           <DollarSign className="w-5 h-5" />
                           Transfer Details
                        </h2>

                        <Input
                           label="Amount"
                           name="amount"
                           type="number"
                           value={formData.amount}
                           onChange={handleInputChange}
                           placeholder="0.00"
                           min="1"
                           step="0.01"
                           required
                        />

                        <div>
                           <label className="text-sm font-medium text-foreground mb-2 block">
                              Description (Optional)
                           </label>
                           <textarea
                              name="description"
                              value={formData.description}
                              onChange={handleInputChange}
                              placeholder="What's this transfer for?"
                              className="w-full p-3 border border-border rounded-lg bg-background text-foreground min-h-[80px] resize-none"
                           />
                        </div>
                     </div>

                     {/* Transfer Summary */}
                     {formData.amount && (
                        <div className="bg-muted/30 rounded-lg p-4">
                           <h3 className="font-medium text-foreground mb-3">
                              Transfer Summary
                           </h3>
                           <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                 <span className="text-muted-foreground">
                                    Transfer Amount:
                                 </span>
                                 <span className="font-medium">
                                    $
                                    {parseFloat(formData.amount || "0").toFixed(
                                       2
                                    )}
                                 </span>
                              </div>
                              <div className="flex justify-between">
                                 <span className="text-muted-foreground">
                                    Transfer Fee:
                                 </span>
                                 <span className="font-medium text-success">
                                    Free
                                 </span>
                              </div>
                              <div className="flex justify-between border-t border-border pt-2">
                                 <span className="text-muted-foreground">
                                    Total:
                                 </span>
                                 <span className="font-semibold">
                                    $
                                    {parseFloat(formData.amount || "0").toFixed(
                                       2
                                    )}
                                 </span>
                              </div>
                              <div className="flex justify-between">
                                 <span className="text-muted-foreground">
                                    Processing Time:
                                 </span>
                                 <span className="font-medium text-success">
                                    Instant
                                 </span>
                              </div>
                           </div>
                        </div>
                     )}

                     {/* Submit Button */}
                     <Button
                        type="submit"
                        variant="premium"
                        size="lg"
                        className="w-full"
                        disabled={
                           !formData.recipientName ||
                           !formData.accountNumber ||
                           !formData.bankName ||
                           !formData.amount ||
                           parseFloat(formData.amount) < 1
                        }
                     >
                        Preview Transfer
                     </Button>
                  </form>

                  {/* Security Notice */}
                  <div className="mt-6 p-4 bg-muted/30 rounded-lg">
                     <p className="text-sm text-muted-foreground text-center">
                        🔒 All transfers are secured with bank-level encryption
                        and require additional verification for amounts over
                        $2,500.
                     </p>
                  </div>
               </div>
            </motion.div>
         </div>

         {/* Transaction Preview Modal */}
         <TransactionPreviewModal
            isOpen={showPreviewModal}
            onClose={() => setShowPreviewModal(false)}
            onConfirm={handleConfirmTransaction}
            isLoading={isProcessing}
            transaction={{
               type: "Local Transfer",
               recipientName: formData.recipientName,
               accountNumber: formData.accountNumber,
               amount: formData.amount,
               description: formData.description,
               fee: "Free",
               total: formData.amount,
               processingTime: "Instant",
            }}
         />
      </div>
   );
}
