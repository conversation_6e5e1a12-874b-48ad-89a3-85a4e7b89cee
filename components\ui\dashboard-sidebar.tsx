"use client";

import {
   <PERSON><PERSON>,
   <PERSON><PERSON><PERSON><PERSON><PERSON>,
   <PERSON><PERSON><PERSON><PERSON>er,
   SidebarGroup,
   SidebarGroupContent,
   SidebarGroupLabel,
   SidebarHeader,
   SidebarMenu,
   SidebarMenuButton,
   SidebarMenuItem,
   SidebarRail,
   useSidebar,
} from "@/components/ui/sidebar";
import { UserDropdown } from "@/components/ui/user-dropdown";
import { useAuth } from "@/hooks";
import { isAdmin } from "@/lib/utils/rbac";
import {
   ArrowLeftRight,
   BarChart3,
   Bell,
   CreditCard,
   Globe,
   HelpCircle,
   History,
   Home,
   Plus,
   Shield,
   User,
   Wallet,
} from "lucide-react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";

interface NavigationItem {
   title: string;
   url: string;
   icon: React.ComponentType<{ className?: string }>;
   isActive?: boolean;
}

interface NavigationGroup {
   title: string;
   items: NavigationItem[];
}

export function DashboardSidebar() {
   const pathname = usePathname();
   const router = useRouter();
   const { user: authUser, logout } = useAuth();
   const { isMobile, setOpenMobile } = useSidebar();

   const user = authUser || {
      id: "USR-2024-001",
      firstName: "Deji",
      lastName: "Ade",
      username: "dejiade",
      email: "<EMAIL>",
      phoneNumber: "***********",
      country: "Nigeria",
      accountType: "Premium",
      role: "user" as const,
      accountNumber: "**********",
      balance: 12847.52,
      accountStatus: "Active",
      verificationStatus: "verified",
   };

   const userDropdownData = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      accountType: user.accountType,
      role: user.role,
      verificationStatus: user.verificationStatus,
   };

   const handleUserProfile = () => {
      router.push("/dashboard/profile");
   };

   const handleUserSupport = () => {
      router.push("/dashboard/support");
   };

   const handleLogout = () => {
      logout();
      router.push("/login");
   };

   const navigationGroups: NavigationGroup[] = [
      {
         title: "Main Menu",
         items: [
            {
               title: "Dashboard",
               url: "/dashboard",
               icon: Home,
               isActive: pathname === "/dashboard",
            },
            {
               title: "Account Overview",
               url: "/dashboard/account-overview",
               icon: BarChart3,
               isActive: pathname === "/dashboard/account-overview",
            },
            // Add admin panel link for admin users
            ...(isAdmin(authUser)
               ? [
                    {
                       title: "Admin Panel",
                       url: "/admin",
                       icon: Shield,
                       isActive: pathname.startsWith("/admin"),
                    },
                 ]
               : []),
         ],
      },
      {
         title: "Transfers",
         items: [
            {
               title: "Local Transfer",
               url: "/dashboard/local-transfer",
               icon: ArrowLeftRight,
               isActive: pathname === "/dashboard/local-transfer",
            },
            {
               title: "International Transfer",
               url: "/dashboard/international-transfer",
               icon: Globe,
               isActive: pathname === "/dashboard/international-transfer",
            },
         ],
      },
      {
         title: "Cards",
         items: [
            {
               title: "Your Cards",
               url: "/dashboard/cards",
               icon: CreditCard,
               isActive: pathname === "/dashboard/cards",
            },
            {
               title: "Apply for Card",
               url: "/dashboard/cards/apply",
               icon: Plus,
               isActive: pathname === "/dashboard/cards/apply",
            },
         ],
      },
      {
         title: "Account Management",
         items: [
            {
               title: "Account History",
               url: "/dashboard/account-history",
               icon: History,
               isActive: pathname === "/dashboard/account-history",
            },
            {
               title: "Deposit",
               url: "/dashboard/deposit",
               icon: Wallet,
               isActive: pathname === "/dashboard/deposit",
            },
            {
               title: "Profile",
               url: "/dashboard/profile",
               icon: User,
               isActive: pathname === "/dashboard/profile",
            },
         ],
      },
      {
         title: "Support & Settings",
         items: [
            {
               title: "Support",
               url: "/dashboard/support",
               icon: HelpCircle,
               isActive: pathname === "/dashboard/support",
            },
            {
               title: "Notifications",
               url: "/dashboard/notifications",
               icon: Bell,
               isActive: pathname === "/dashboard/notifications",
            },
         ],
      },
   ];

   return (
      <Sidebar
         variant="inset"
         className="border-r border-border"
         role="navigation"
         aria-label="Dashboard navigation"
      >
         <SidebarHeader className="border-b border-border bg-card">
            <div className="flex h-16 items-center px-4">
               <Image
                  src="/images/logo.svg"
                  alt="Paramount Bank Logo"
                  width={100}
                  height={100}
                  className="h-8 w-auto"
               />
            </div>
         </SidebarHeader>

         <SidebarContent>
            {navigationGroups.map((group) => (
               <SidebarGroup key={group.title}>
                  <SidebarGroupLabel className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
                     {group.title}
                  </SidebarGroupLabel>
                  <SidebarGroupContent>
                     <SidebarMenu>
                        {group.items.map((item) => {
                           const IconComponent = item.icon;
                           return (
                              <SidebarMenuItem key={item.title}>
                                 <SidebarMenuButton
                                    isActive={item.isActive}
                                    onClick={() => {
                                       router.push(item.url);
                                       // Close sidebar on mobile after navigation
                                       if (isMobile) {
                                          setOpenMobile(false);
                                       }
                                    }}
                                    className="w-full justify-start"
                                    aria-label={`Navigate to ${item.title}`}
                                 >
                                    <IconComponent
                                       className="h-4 w-4"
                                       aria-hidden="true"
                                    />
                                    <span>{item.title}</span>
                                 </SidebarMenuButton>
                              </SidebarMenuItem>
                           );
                        })}
                     </SidebarMenu>
                  </SidebarGroupContent>
               </SidebarGroup>
            ))}
         </SidebarContent>

         <SidebarFooter className="border-t border-border bg-card">
            <div className="p-4">
               <UserDropdown
                  user={userDropdownData}
                  onProfile={handleUserProfile}
                  onSupport={handleUserSupport}
                  onLogout={handleLogout}
               />
            </div>
         </SidebarFooter>

         <SidebarRail />
      </Sidebar>
   );
}
