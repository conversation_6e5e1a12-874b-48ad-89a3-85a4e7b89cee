import {
   CreateUserNotificationData,
   NotificationFilters,
   UpdateUserNotificationData,
   UserNotification,
   UserNotificationDetails,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { ObjectId, OptionalId } from "mongodb";

/**
 * Create a new user notification
 */
export async function createUserNotification(
   data: CreateUserNotificationData
): Promise<{
   success: boolean;
   notification?: UserNotificationDetails;
   message?: string;
}> {
   try {
      const notificationsCollection = await getCollection<UserNotification>(
         "user_notifications"
      );

      const metadata = data.metadata
         ? {
              ...data.metadata,
              transactionId: data.metadata.transactionId
                 ? new ObjectId(data.metadata.transactionId)
                 : undefined,
              accountId: data.metadata.accountId
                 ? new ObjectId(data.metadata.accountId)
                 : undefined,
              cardId: data.metadata.cardId
                 ? new ObjectId(data.metadata.cardId)
                 : undefined,
           }
         : undefined;

      const newNotification: OptionalId<UserNotification> = {
         userId: data.userId ? new ObjectId(data.userId) : new ObjectId(),
         title: data.title,
         message: data.message,
         type: data.type,
         category: data.category,
         isRead: false,
         isGlobal: data.isGlobal || false,
         metadata,
         createdAt: new Date(),
         updatedAt: new Date(),
         createdBy: data.createdBy ? new ObjectId(data.createdBy) : undefined,
         expiresAt: data.expiresAt,
      };

      const result = await notificationsCollection.insertOne(newNotification);

      if (result.insertedId) {
         const notificationDetails: UserNotificationDetails = {
            id: result.insertedId.toString(),
            userId: newNotification.userId.toString(),
            title: newNotification.title,
            message: newNotification.message,
            type: newNotification.type,
            category: newNotification.category,
            isRead: newNotification.isRead,
            isGlobal: newNotification.isGlobal,
            metadata: newNotification.metadata
               ? {
                    ...newNotification.metadata,
                    transactionId: newNotification.metadata.transactionId
                       ? newNotification.metadata.transactionId.toString()
                       : undefined,
                    accountId: newNotification.metadata.accountId
                       ? newNotification.metadata.accountId.toString()
                       : undefined,
                    cardId: newNotification.metadata.cardId
                       ? newNotification.metadata.cardId.toString()
                       : undefined,
                 }
               : undefined,
            createdAt: newNotification.createdAt,
            updatedAt: newNotification.updatedAt,
            createdBy: newNotification.createdBy
               ? newNotification.createdBy.toString()
               : undefined,
            expiresAt: newNotification.expiresAt,
         };

         return {
            success: true,
            notification: notificationDetails,
            message: "Notification created successfully",
         };
      }

      return {
         success: false,
         message: "Failed to create notification",
      };
   } catch (error) {
      console.error("Error creating user notification:", error);
      return {
         success: false,
         message: "Failed to create notification",
      };
   }
}

/**
 * Create global notification for all users
 */
export async function createGlobalNotification(
   data: Omit<CreateUserNotificationData, "userId" | "isGlobal"> & {
      createdBy: string;
   }
): Promise<{
   success: boolean;
   message?: string;
   notificationsCreated?: number;
}> {
   try {
      // Get all user IDs
      const usersCollection = await getCollection("users");
      const users = await usersCollection
         .find({}, { projection: { _id: 1 } })
         .toArray();

      if (users.length === 0) {
         return {
            success: false,
            message: "No users found to send notifications to",
         };
      }

      const notificationsCollection = await getCollection<UserNotification>(
         "user_notifications"
      );

      const metadata = data.metadata
         ? {
              ...data.metadata,
              transactionId: data.metadata.transactionId
                 ? new ObjectId(data.metadata.transactionId)
                 : undefined,
              accountId: data.metadata.accountId
                 ? new ObjectId(data.metadata.accountId)
                 : undefined,
              cardId: data.metadata.cardId
                 ? new ObjectId(data.metadata.cardId)
                 : undefined,
           }
         : undefined;

      // Create notifications for all users
      const notifications: OptionalId<UserNotification>[] = users.map(
         (user) => ({
            userId: user._id,
            title: data.title,
            message: data.message,
            type: data.type,
            category: data.category,
            isRead: false,
            isGlobal: true,
            metadata,
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: new ObjectId(data.createdBy),
            expiresAt: data.expiresAt,
         })
      );

      const result = await notificationsCollection.insertMany(notifications);

      return {
         success: true,
         message: "Global notifications created successfully",
         notificationsCreated: result.insertedCount,
      };
   } catch (error) {
      console.error("Error creating global notification:", error);
      return {
         success: false,
         message: "Failed to create global notifications",
      };
   }
}

/**
 * Get user notifications with filtering and pagination
 */
export async function getUserNotifications(
   filters: NotificationFilters
): Promise<{
   success: boolean;
   notifications?: UserNotificationDetails[];
   total?: number;
   message?: string;
}> {
   try {
      const notificationsCollection = await getCollection<UserNotification>(
         "user_notifications"
      );

      // Build query
      const query: any = {};

      if (filters.userId) {
         query.userId = new ObjectId(filters.userId);
      }

      if (filters.type) {
         query.type = filters.type;
      }

      if (filters.category) {
         query.category = filters.category;
      }

      if (filters.isRead !== undefined) {
         query.isRead = filters.isRead;
      }

      if (filters.isGlobal !== undefined) {
         query.isGlobal = filters.isGlobal;
      }

      if (filters.dateFrom || filters.dateTo) {
         query.createdAt = {};
         if (filters.dateFrom) {
            query.createdAt.$gte = filters.dateFrom;
         }
         if (filters.dateTo) {
            query.createdAt.$lte = filters.dateTo;
         }
      }

      // Remove expired notifications
      query.$or = [
         { expiresAt: { $exists: false } },
         { expiresAt: null },
         { expiresAt: { $gt: new Date() } },
      ];

      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const skip = (page - 1) * limit;

      const [notifications, total] = await Promise.all([
         notificationsCollection
            .find(query)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .toArray(),
         notificationsCollection.countDocuments(query),
      ]);

      const notificationDetails: UserNotificationDetails[] = notifications.map(
         (notification) => ({
            id: notification._id!.toString(),
            userId: notification.userId.toString(),
            title: notification.title,
            message: notification.message,
            type: notification.type,
            category: notification.category,
            isRead: notification.isRead,
            isGlobal: notification.isGlobal,
            metadata: notification.metadata
               ? {
                    ...notification.metadata,
                    transactionId: notification.metadata.transactionId
                       ? notification.metadata.transactionId.toString()
                       : undefined,
                    accountId: notification.metadata.accountId
                       ? notification.metadata.accountId.toString()
                       : undefined,
                    cardId: notification.metadata.cardId
                       ? notification.metadata.cardId.toString()
                       : undefined,
                 }
               : undefined,
            createdAt: notification.createdAt,
            updatedAt: notification.updatedAt,
            createdBy: notification.createdBy
               ? notification.createdBy.toString()
               : undefined,
            expiresAt: notification.expiresAt,
         })
      );

      return {
         success: true,
         notifications: notificationDetails,
         total,
      };
   } catch (error) {
      console.error("Error getting user notifications:", error);
      return {
         success: false,
         message: "Failed to get notifications",
      };
   }
}

/**
 * Mark notification as read
 */
export async function markNotificationAsRead(notificationId: string): Promise<{
   success: boolean;
   message?: string;
}> {
   try {
      const notificationsCollection = await getCollection<UserNotification>(
         "user_notifications"
      );

      const result = await notificationsCollection.updateOne(
         { _id: new ObjectId(notificationId) },
         {
            $set: {
               isRead: true,
               updatedAt: new Date(),
            },
         }
      );

      if (result.matchedCount === 0) {
         return {
            success: false,
            message: "Notification not found",
         };
      }

      return {
         success: true,
         message: "Notification marked as read",
      };
   } catch (error) {
      console.error("Error marking notification as read:", error);
      return {
         success: false,
         message: "Failed to mark notification as read",
      };
   }
}

/**
 * Mark all notifications as read for a user
 */
export async function markAllNotificationsAsRead(userId: string): Promise<{
   success: boolean;
   message?: string;
   updatedCount?: number;
}> {
   try {
      const notificationsCollection = await getCollection<UserNotification>(
         "user_notifications"
      );

      const result = await notificationsCollection.updateMany(
         {
            userId: new ObjectId(userId),
            isRead: false,
         },
         {
            $set: {
               isRead: true,
               updatedAt: new Date(),
            },
         }
      );

      return {
         success: true,
         message: "All notifications marked as read",
         updatedCount: result.modifiedCount,
      };
   } catch (error) {
      console.error("Error marking all notifications as read:", error);
      return {
         success: false,
         message: "Failed to mark all notifications as read",
      };
   }
}

/**
 * Delete notification
 */
export async function deleteNotification(notificationId: string): Promise<{
   success: boolean;
   message?: string;
}> {
   try {
      const notificationsCollection = await getCollection<UserNotification>(
         "user_notifications"
      );

      const result = await notificationsCollection.deleteOne({
         _id: new ObjectId(notificationId),
      });

      if (result.deletedCount === 0) {
         return {
            success: false,
            message: "Notification not found",
         };
      }

      return {
         success: true,
         message: "Notification deleted successfully",
      };
   } catch (error) {
      console.error("Error deleting notification:", error);
      return {
         success: false,
         message: "Failed to delete notification",
      };
   }
}

/**
 * Get unread notification count for a user
 */
export async function getUnreadNotificationCount(userId: string): Promise<{
   success: boolean;
   count?: number;
   message?: string;
}> {
   try {
      const notificationsCollection = await getCollection<UserNotification>(
         "user_notifications"
      );

      const count = await notificationsCollection.countDocuments({
         userId: new ObjectId(userId),
         isRead: false,
         $or: [
            { expiresAt: { $exists: false } },
            { expiresAt: null },
            { expiresAt: { $gt: new Date() } },
         ],
      });

      return {
         success: true,
         count,
      };
   } catch (error) {
      console.error("Error getting unread notification count:", error);
      return {
         success: false,
         message: "Failed to get unread notification count",
      };
   }
}

/**
 * Clean up expired notifications
 */
export async function cleanupExpiredNotifications(): Promise<{
   success: boolean;
   deletedCount?: number;
   message?: string;
}> {
   try {
      const notificationsCollection = await getCollection<UserNotification>(
         "user_notifications"
      );

      const result = await notificationsCollection.deleteMany({
         expiresAt: { $lte: new Date() },
      });

      return {
         success: true,
         deletedCount: result.deletedCount,
         message: `Cleaned up ${result.deletedCount} expired notifications`,
      };
   } catch (error) {
      console.error("Error cleaning up expired notifications:", error);
      return {
         success: false,
         message: "Failed to clean up expired notifications",
      };
   }
}
