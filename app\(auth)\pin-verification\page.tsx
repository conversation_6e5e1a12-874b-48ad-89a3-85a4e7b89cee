"use client";

import { But<PERSON> } from "@/components/ui/button";
import { PinInput } from "@/components/ui/input";
import { useAuth } from "@/hooks";
import { motion } from "framer-motion";
import { ArrowLeft, RefreshCw, Shield } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function PinVerification() {
   const [pin, setPin] = useState("");
   const [error, setError] = useState("");
   const [attempts, setAttempts] = useState(0);
   const [isLocked, setIsLocked] = useState(false);
   const [lockTimeRemaining, setLockTimeRemaining] = useState(0);

   const router = useRouter();
   const {
      isAuthenticated,
      isPinVerified,
      user,
      verifyPin,
      isVerifyingPin,
      pinError,
      resetPinError,
      isHydrated,
   } = useAuth();

   // Redirect if not authenticated or already PIN verified
   useEffect(() => {
      // Only redirect after hydration to prevent SSR/client mismatch
      if (!isHydrated) return;

      if (!isAuthenticated) {
         router.replace("/login");
      } else if (isPinVerified) {
         router.replace("/dashboard");
      }
   }, [isAuthenticated, isPinVerified, isHydrated, router]);

   // Handle lockout timer
   useEffect(() => {
      let interval: NodeJS.Timeout;
      if (isLocked && lockTimeRemaining > 0) {
         interval = setInterval(() => {
            setLockTimeRemaining((prev) => {
               if (prev <= 1) {
                  setIsLocked(false);
                  setAttempts(0);
                  return 0;
               }
               return prev - 1;
            });
         }, 1000);
      }
      return () => clearInterval(interval);
   }, [isLocked, lockTimeRemaining]);

   const handlePinSubmit = async () => {
      if (pin.length !== 4) {
         setError("Please enter a 4-digit PIN");
         return;
      }

      if (isLocked) {
         setError(
            `Too many attempts. Try again in ${lockTimeRemaining} seconds.`
         );
         return;
      }

      setError("");
      resetPinError(); // Clear any previous errors

      try {
         await verifyPin(pin);
         // PIN verified successfully
         router.push("/dashboard");
      } catch (error) {
         console.error("PIN verification error:", error);
         const newAttempts = attempts + 1;
         setAttempts(newAttempts);

         if (newAttempts >= 3) {
            setIsLocked(true);
            setLockTimeRemaining(300); // 5 minutes lockout
            setError("Too many failed attempts. Account locked for 5 minutes.");
         } else {
            setError(
               pinError ||
                  `Incorrect PIN. ${3 - newAttempts} attempts remaining.`
            );
         }
         setPin("");
      }
   };

   const formatTime = (seconds: number) => {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
   };

   if (!isAuthenticated) {
      return null; // Will redirect
   }

   return (
      <div className="min-h-screen bg-gradient-to-br from-background via-secondary/30 to-background flex items-center justify-center p-4">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="w-full max-w-md"
         >
            <div className="text-center mb-8 lg:hidden">
               <Image
                  src="/images/logo.svg"
                  alt="Paramount Bank Logo"
                  width={100}
                  height={100}
                  className="h-8 w-auto"
               />
            </div>

            <div className="bank-card p-8">
               <div className="text-center mb-8">
                  <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                     <Shield className="w-8 h-8 text-primary" />
                  </div>
                  <h1 className="text-2xl font-bold text-foreground mb-2">
                     PIN Verification
                  </h1>
                  <p className="text-muted-foreground">
                     Enter your 4-digit PIN to access your account
                  </p>
                  {user && (
                     <p className="text-sm text-muted-foreground mt-2">
                        Welcome back, {user.firstName}
                     </p>
                  )}
               </div>

               <div className="space-y-6">
                  {pinError && !error && (
                     <div className="p-3 rounded-lg bg-destructive/10 border border-destructive/20">
                        <p className="text-sm text-destructive">{pinError}</p>
                     </div>
                  )}

                  <PinInput
                     length={4}
                     value={pin}
                     onChange={setPin}
                     error={error}
                     disabled={isVerifyingPin || isLocked}
                  />

                  {isLocked && (
                     <div className="text-center p-4 bg-destructive/10 rounded-lg border border-destructive/20">
                        <p className="text-sm text-destructive">
                           Account temporarily locked
                        </p>
                        <p className="text-xs text-destructive/80 mt-1">
                           Time remaining: {formatTime(lockTimeRemaining)}
                        </p>
                     </div>
                  )}

                  <Button
                     onClick={handlePinSubmit}
                     variant="premium"
                     size="lg"
                     className="w-full"
                     disabled={isVerifyingPin || pin.length !== 4 || isLocked}
                  >
                     {isVerifyingPin ? (
                        <>
                           <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                           Verifying...
                        </>
                     ) : (
                        "Verify PIN"
                     )}
                  </Button>

                  <div className="flex items-center justify-between text-sm">
                     <Button
                        variant="ghost"
                        onClick={() => router.push("/login")}
                        className="text-muted-foreground hover:text-foreground"
                     >
                        <ArrowLeft className="w-4 h-4 mr-1" />
                        Back to Login
                     </Button>
                  </div>
               </div>
            </div>

            <div className="mt-6 text-center">
               <p className="text-xs text-muted-foreground mt-1">
                  Protected by 256-bit SSL encryption
               </p>
            </div>
         </motion.div>
      </div>
   );
}
