"use client";

import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import {
   AlertCircle,
   CheckCircle,
   ChevronDown,
   HelpCircle,
   LogOut,
   Settings,
} from "lucide-react";
import * as React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "./avatar";
import { Badge } from "./badge";
import { Button } from "./button";
import { Separator } from "./separator";

export interface UserInfo {
   id: string;
   firstName: string;
   lastName: string;
   email: string;
   avatar?: string;
   accountType: string;
   verificationStatus: "verified" | "pending" | "unverified";
}

export interface UserDropdownProps {
   user: UserInfo;
   onProfile: () => void;
   onSupport: () => void;
   onLogout: () => void;
   className?: string;
}

const UserDropdown = React.forwardRef<HTMLDivElement, UserDropdownProps>(
   ({ user, onProfile, onSupport, onLogout, className }, ref) => {
      const [isOpen, setIsOpen] = React.useState(false);

      const getVerificationBadge = () => {
         switch (user.verificationStatus) {
            case "verified":
               return (
                  <Badge
                     variant="secondary"
                     className="bg-success/10 text-success border-success/20"
                  >
                     <CheckCircle className="w-3 h-3 mr-1" />
                     Verified
                  </Badge>
               );
            case "pending":
               return (
                  <Badge
                     variant="secondary"
                     className="bg-warning/10 text-warning border-warning/20"
                  >
                     <AlertCircle className="w-3 h-3 mr-1" />
                     Pending
                  </Badge>
               );
            default:
               return (
                  <Badge
                     variant="secondary"
                     className="bg-destructive/10 text-destructive border-destructive/20"
                  >
                     <AlertCircle className="w-3 h-3 mr-1" />
                     Unverified
                  </Badge>
               );
         }
      };

      const getInitials = () => {
         return `${user.firstName.charAt(0)}${user.lastName.charAt(
            0
         )}`.toUpperCase();
      };

      return (
         <div className={cn("relative", className)} ref={ref}>
            <Button
               variant="ghost"
               onClick={() => setIsOpen(!isOpen)}
               className="flex items-center gap-2 h-auto p-2"
            >
               <Avatar className="h-8 w-8">
                  <AvatarImage
                     src={user.avatar}
                     alt={`${user.firstName} ${user.lastName}`}
                  />
                  <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                     {getInitials()}
                  </AvatarFallback>
               </Avatar>
               <div className="hidden md:block text-left">
                  <p className="text-sm font-medium text-foreground">
                     {user.firstName} {user.lastName}
                  </p>
                  <p className="text-xs text-muted-foreground">
                     {user.accountType}
                  </p>
               </div>
               <ChevronDown className="h-4 w-4 text-muted-foreground" />
            </Button>

            <AnimatePresence>
               {isOpen && (
                  <>
                     <div
                        className="fixed inset-0 z-40"
                        onClick={() => setIsOpen(false)}
                     />
                     <motion.div
                        initial={{ opacity: 0, scale: 0.95, y: -10 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.95, y: -10 }}
                        transition={{ duration: 0.2 }}
                        className="absolute -left-5 bottom-[120%] mt-2 w-60 bg-popover border border-border rounded-lg shadow-lg z-50"
                     >
                        <div className="p-4">
                           {/* User Info Section */}
                           <div className="flex items-center gap-3 mb-4">
                              <Avatar className="h-12 w-12">
                                 <AvatarImage
                                    src={user.avatar}
                                    alt={`${user.firstName} ${user.lastName}`}
                                 />
                                 <AvatarFallback className="bg-primary text-primary-foreground">
                                    {getInitials()}
                                 </AvatarFallback>
                              </Avatar>
                              <div className="flex-1 min-w-0">
                                 <p className="font-semibold text-foreground truncate">
                                    {user.firstName} {user.lastName}
                                 </p>
                                 <p className="text-sm text-muted-foreground truncate">
                                    ID: {user.id}
                                 </p>
                                 <p className="text-xs text-muted-foreground truncate">
                                    {user.email}
                                 </p>
                              </div>
                           </div>

                           {/* Verification Status */}
                           <div className="mb-4">{getVerificationBadge()}</div>

                           <Separator className="mb-4" />

                           {/* Action Buttons */}
                           <div className="space-y-1">
                              <Button
                                 variant="ghost"
                                 onClick={() => {
                                    onSupport();
                                    setIsOpen(false);
                                 }}
                                 className="w-full justify-start"
                              >
                                 <HelpCircle className="w-4 h-4 mr-3" />
                                 Support
                              </Button>

                              <Button
                                 variant="ghost"
                                 onClick={() => {
                                    onProfile();
                                    setIsOpen(false);
                                 }}
                                 className="w-full justify-start"
                              >
                                 <Settings className="w-4 h-4 mr-3" />
                                 Profile Settings
                              </Button>

                              <Separator className="my-2" />

                              <Button
                                 variant="ghost"
                                 onClick={() => {
                                    onLogout();
                                    setIsOpen(false);
                                 }}
                                 className="w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10"
                              >
                                 <LogOut className="w-4 h-4 mr-3" />
                                 Sign Out
                              </Button>
                           </div>
                        </div>
                     </motion.div>
                  </>
               )}
            </AnimatePresence>
         </div>
      );
   }
);

UserDropdown.displayName = "UserDropdown";

export { UserDropdown };
