/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { useUsers } from "@/hooks";
import { motion } from "framer-motion";
import {
   AlertTriangle,
   Clock,
   Crown,
   Eye,
   Search,
   Settings,
   Shield,
   ShieldCheck,
   ShieldX,
   User,
   Users,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function AdminUsers() {
   const router = useRouter();
   const [searchTerm, setSearchTerm] = useState("");
   const [roleFilter, setRoleFilter] = useState<"all" | "user" | "admin">(
      "all"
   );
   const [statusFilter, setStatusFilter] = useState<
      "all" | "verified" | "pending" | "unverified"
   >("all");
   const [currentPage, setCurrentPage] = useState(1);
   const pageSize = 20;

   // Get users with filtering
   const {
      data: usersData,
      isLoading,
      error,
      refetch,
   } = useUsers({
      search: searchTerm,
      role: roleFilter === "all" ? undefined : roleFilter,
      verificationStatus: statusFilter === "all" ? undefined : statusFilter,
      page: currentPage,
      limit: pageSize,
   });

   const users = usersData?.users || [];
   const totalUsers = usersData?.total || 0;
   const totalPages = Math.ceil(totalUsers / pageSize);

   const getStatusIcon = (status: string) => {
      switch (status) {
         case "verified":
            return <ShieldCheck className="h-4 w-4 text-green-600" />;
         case "pending":
            return <Clock className="h-4 w-4 text-yellow-600" />;
         case "unverified":
            return <ShieldX className="h-4 w-4 text-red-600" />;
         default:
            return <Shield className="h-4 w-4 text-gray-600" />;
      }
   };

   const getStatusBadge = (status: string) => {
      const variants = {
         verified: {
            variant: "default" as const,
            className: "bg-green-100 text-green-800 border-green-200",
         },
         pending: {
            variant: "secondary" as const,
            className: "bg-yellow-100 text-yellow-800 border-yellow-200",
         },
         unverified: {
            variant: "destructive" as const,
            className: "bg-red-100 text-red-800 border-red-200",
         },
      };

      const config =
         variants[status as keyof typeof variants] || variants.unverified;

      return (
         <Badge variant={config.variant} className={config.className}>
            {getStatusIcon(status)}
            <span className="ml-1 capitalize">{status}</span>
         </Badge>
      );
   };

   const getRoleBadge = (role?: string) => {
      return (
         <Badge variant={role === "admin" ? "destructive" : "outline"}>
            {role === "admin" ? (
               <Crown className="h-3 w-3 mr-1" />
            ) : (
               <User className="h-3 w-3 mr-1" />
            )}
            {role ? role.charAt(0).toUpperCase() + role.slice(1) : "User"}
         </Badge>
      );
   };

   const handleViewUser = (userId: string) => {
      router.push(`/admin/users/${userId}`);
   };

   const handleManageVerification = () => {
      router.push("/admin/users/verification");
   };

   const handleManageRoles = () => {
      router.push("/admin/users/roles");
   };

   const stats = {
      total: totalUsers,
      verified: users.filter((u) => u.verificationStatus === "verified").length,
      pending: users.filter((u) => u.verificationStatus === "pending").length,
      unverified: users.filter((u) => u.verificationStatus === "unverified")
         .length,
      admins: users.filter((u) => u.role === "admin").length,
      regularUsers: users.filter((u) => u.role === "user").length,
   };

   return (
      <div className="container mx-auto px-4 py-8">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
         >
            {/* Header */}
            <div className="mb-8">
               <h1 className="text-3xl font-bold text-foreground mb-2">
                  User Management
               </h1>
               <p className="text-muted-foreground">
                  Manage all users, their verification status, and roles in the
                  system.
               </p>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap gap-4 mb-8">
               <Button
                  onClick={handleManageVerification}
                  className="flex items-center gap-2"
               >
                  <ShieldCheck className="h-4 w-4" />
                  Manage Verification
               </Button>
               <Button
                  onClick={handleManageRoles}
                  variant="outline"
                  className="flex items-center gap-2"
               >
                  <Crown className="h-4 w-4" />
                  Manage Roles
               </Button>
               <Button variant="outline" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  User Settings
               </Button>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <Users className="h-5 w-5 text-muted-foreground" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Total
                           </p>
                           <p className="text-xl font-bold">{stats.total}</p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <User className="h-5 w-5 text-blue-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Users
                           </p>
                           <p className="text-xl font-bold text-blue-600">
                              {stats.regularUsers}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <Crown className="h-5 w-5 text-purple-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Admins
                           </p>
                           <p className="text-xl font-bold text-purple-600">
                              {stats.admins}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <ShieldCheck className="h-5 w-5 text-green-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Verified
                           </p>
                           <p className="text-xl font-bold text-green-600">
                              {stats.verified}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <Clock className="h-5 w-5 text-yellow-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Pending
                           </p>
                           <p className="text-xl font-bold text-yellow-600">
                              {stats.pending}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <ShieldX className="h-5 w-5 text-red-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Unverified
                           </p>
                           <p className="text-xl font-bold text-red-600">
                              {stats.unverified}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
            </div>

            {/* Filters */}
            <Card className="mb-6">
               <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                     <Search className="h-5 w-5" />
                     Search & Filter
                  </CardTitle>
               </CardHeader>
               <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                     <div>
                        <Label htmlFor="search">Search Users</Label>
                        <Input
                           id="search"
                           placeholder="Search by name or email..."
                           value={searchTerm}
                           onChange={(e) => setSearchTerm(e.target.value)}
                           className="mt-1"
                        />
                     </div>
                     <div>
                        <Label htmlFor="role-filter">Role</Label>
                        <Select
                           value={roleFilter}
                           onValueChange={(value: any) => setRoleFilter(value)}
                        >
                           <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Filter by role" />
                           </SelectTrigger>
                           <SelectContent>
                              <SelectItem value="all">All Roles</SelectItem>
                              <SelectItem value="user">Users</SelectItem>
                              <SelectItem value="admin">Admins</SelectItem>
                           </SelectContent>
                        </Select>
                     </div>
                     <div>
                        <Label htmlFor="status-filter">
                           Verification Status
                        </Label>
                        <Select
                           value={statusFilter}
                           onValueChange={(value: any) =>
                              setStatusFilter(value)
                           }
                        >
                           <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Filter by status" />
                           </SelectTrigger>
                           <SelectContent>
                              <SelectItem value="all">All Statuses</SelectItem>
                              <SelectItem value="verified">Verified</SelectItem>
                              <SelectItem value="pending">Pending</SelectItem>
                              <SelectItem value="unverified">
                                 Unverified
                              </SelectItem>
                           </SelectContent>
                        </Select>
                     </div>
                  </div>
               </CardContent>
            </Card>

            {/* Users List */}
            <Card>
               <CardHeader>
                  <div className="flex items-center justify-between">
                     <CardTitle>Users ({totalUsers})</CardTitle>
                     <div className="text-sm text-muted-foreground">
                        Page {currentPage} of {totalPages}
                     </div>
                  </div>
               </CardHeader>
               <CardContent>
                  {isLoading ? (
                     <div className="space-y-4">
                        {Array.from({ length: 5 }).map((_, index) => (
                           <div
                              key={index}
                              className="flex items-center gap-4 p-4 border rounded-lg"
                           >
                              <Skeleton className="h-10 w-10 rounded-full" />
                              <div className="flex-1 space-y-2">
                                 <Skeleton className="h-4 w-48" />
                                 <Skeleton className="h-3 w-32" />
                              </div>
                              <Skeleton className="h-6 w-20" />
                              <Skeleton className="h-6 w-16" />
                              <Skeleton className="h-8 w-20" />
                           </div>
                        ))}
                     </div>
                  ) : error ? (
                     <div className="text-center py-8">
                        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                        <p className="text-red-600 mb-4">
                           Failed to load users
                        </p>
                        <Button onClick={() => refetch()} variant="outline">
                           Try Again
                        </Button>
                     </div>
                  ) : users.length === 0 ? (
                     <div className="text-center py-8">
                        <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground">No users found</p>
                     </div>
                  ) : (
                     <>
                        <div className="space-y-4">
                           {users.map((user) => (
                              <div
                                 key={user.id}
                                 className="flex items-center gap-4 p-4 border rounded-lg hover:bg-muted/30 transition-colors"
                              >
                                 <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                                    <User className="w-5 h-5 text-primary" />
                                 </div>
                                 <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                       <p className="font-medium">
                                          {user.firstName} {user.lastName}
                                       </p>
                                       {getRoleBadge(user.role)}
                                       {getStatusBadge(user.verificationStatus)}
                                    </div>
                                    <p className="text-sm text-muted-foreground">
                                       {user.email}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                       Account: {user.accountType} • Country:{" "}
                                       {user.country}
                                    </p>
                                 </div>
                                 <div className="flex gap-2">
                                    <Button
                                       size="sm"
                                       variant="outline"
                                       onClick={() => handleViewUser(user.id)}
                                    >
                                       <Eye className="w-4 h-4 mr-1" />
                                       View
                                    </Button>
                                 </div>
                              </div>
                           ))}
                        </div>

                        {/* Pagination */}
                        {totalPages > 1 && (
                           <div className="flex items-center justify-between mt-6">
                              <Button
                                 variant="outline"
                                 onClick={() =>
                                    setCurrentPage((prev) =>
                                       Math.max(1, prev - 1)
                                    )
                                 }
                                 disabled={currentPage === 1}
                              >
                                 Previous
                              </Button>
                              <div className="flex items-center gap-2">
                                 {Array.from(
                                    { length: Math.min(5, totalPages) },
                                    (_, i) => {
                                       const page = i + 1;
                                       return (
                                          <Button
                                             key={page}
                                             variant={
                                                currentPage === page
                                                   ? "default"
                                                   : "outline"
                                             }
                                             size="sm"
                                             onClick={() =>
                                                setCurrentPage(page)
                                             }
                                          >
                                             {page}
                                          </Button>
                                       );
                                    }
                                 )}
                              </div>
                              <Button
                                 variant="outline"
                                 onClick={() =>
                                    setCurrentPage((prev) =>
                                       Math.min(totalPages, prev + 1)
                                    )
                                 }
                                 disabled={currentPage === totalPages}
                              >
                                 Next
                              </Button>
                           </div>
                        )}
                     </>
                  )}
               </CardContent>
            </Card>
         </motion.div>
      </div>
   );
}
