"use client";

import {
   changeTransactionStatus,
   changeUserRole,
   createNotificationMessageAction,
   getCards,
   getNotificationMessages,
   getPendingTransactions,
   getTransactions,
   getUserAccount,
   getUsers,
   modifyAccountBalance,
   updateUserVerification,
} from "@/lib/actions";
import { CreateNotificationMessageData } from "@/lib/models";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// ============================================================================
// USER MANAGEMENT HOOKS
// ============================================================================

/**
 * Hook to get all users with pagination and filtering
 */
export function useUsers(options: {
   page?: number;
   limit?: number;
   search?: string;
   role?: "user" | "admin";
   verificationStatus?: "verified" | "pending" | "unverified";
}) {
   return useQuery({
      queryKey: ["admin", "users", options],
      queryFn: () => getUsers(options),
      staleTime: 30 * 1000, // 30 seconds
   });
}

/**
 * Hook to update user verification status
 */
export function useUpdateUserVerification() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async ({
         userId,
         verificationStatus,
         adminId,
         reason,
      }: {
         userId: string;
         verificationStatus: "verified" | "pending" | "unverified";
         adminId: string;
         reason?: string;
      }) => {
         const result = await updateUserVerification(
            userId,
            verificationStatus,
            adminId,
            reason
         );
         if (!result.success) {
            throw new Error(
               result.message || "Failed to update verification status"
            );
         }
         return result.user!;
      },
      onSuccess: () => {
         // Invalidate users queries
         queryClient.invalidateQueries({ queryKey: ["admin", "users"] });
      },
   });
}

/**
 * Hook to change user role
 */
export function useChangeUserRole() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async ({
         userId,
         role,
         adminId,
         reason,
      }: {
         userId: string;
         role: "user" | "admin";
         adminId: string;
         reason?: string;
      }) => {
         const result = await changeUserRole(userId, role, adminId, reason);
         if (!result.success) {
            throw new Error(result.message || "Failed to change user role");
         }
         return result.user!;
      },
      onSuccess: () => {
         // Invalidate users queries
         queryClient.invalidateQueries({ queryKey: ["admin", "users"] });
      },
   });
}

// ============================================================================
// ACCOUNT MANAGEMENT HOOKS
// ============================================================================

/**
 * Hook to get user account details
 */
export function useUserAccount(userId: string) {
   return useQuery({
      queryKey: ["admin", "account", userId],
      queryFn: async () => {
         const result = await getUserAccount(userId);
         if (!result.success) {
            throw new Error(result.message || "Failed to get account details");
         }
         return result.account!;
      },
      enabled: !!userId,
      staleTime: 60 * 1000, // 1 minute
   });
}

/**
 * Hook to modify account balance
 */
export function useModifyAccountBalance() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async ({
         userId,
         amount,
         type,
         adminId,
         reason,
      }: {
         userId: string;
         amount: number;
         type: "credit" | "debit";
         adminId: string;
         reason: string;
      }) => {
         const result = await modifyAccountBalance(
            userId,
            amount,
            type,
            adminId,
            reason
         );
         if (!result.success) {
            throw new Error(
               result.message || "Failed to modify account balance"
            );
         }
         return result.account!;
      },
      onSuccess: (_, variables) => {
         // Invalidate account queries
         queryClient.invalidateQueries({
            queryKey: ["admin", "account", variables.userId],
         });
         queryClient.invalidateQueries({ queryKey: ["admin", "users"] });
      },
   });
}

// ============================================================================
// TRANSACTION MANAGEMENT HOOKS
// ============================================================================

/**
 * Hook to get all transactions with filtering
 */
export function useTransactions(options: {
   page?: number;
   limit?: number;
   status?: "pending" | "completed" | "failed" | "cancelled";
   type?: "credit" | "debit";
   userId?: string;
   dateFrom?: Date;
   dateTo?: Date;
}) {
   return useQuery({
      queryKey: ["admin", "transactions", options],
      queryFn: async () => {
         const result = await getTransactions(options);
         if (!result.success) {
            throw new Error(result.message || "Failed to get transactions");
         }
         return { transactions: result.transactions!, total: result.total! };
      },
      staleTime: 30 * 1000, // 30 seconds
   });
}

/**
 * Hook to change transaction status
 */
export function useChangeTransactionStatus() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async ({
         transactionId,
         status,
         adminId,
         reason,
      }: {
         transactionId: string;
         status: "pending" | "completed" | "failed" | "cancelled";
         adminId: string;
         reason?: string;
      }) => {
         const result = await changeTransactionStatus(
            transactionId,
            status,
            adminId,
            reason
         );
         if (!result.success) {
            throw new Error(
               result.message || "Failed to change transaction status"
            );
         }
         return result.transaction!;
      },
      onSuccess: () => {
         // Invalidate transaction queries
         queryClient.invalidateQueries({ queryKey: ["admin", "transactions"] });
         queryClient.invalidateQueries({
            queryKey: ["admin", "pending-transactions"],
         });
      },
   });
}

/**
 * Hook to get pending transactions count
 */
export function usePendingTransactions() {
   return useQuery({
      queryKey: ["admin", "pending-transactions"],
      queryFn: async () => {
         const result = await getPendingTransactions();
         if (!result.success) {
            throw new Error(
               result.message || "Failed to get pending transactions"
            );
         }
         return result.count!;
      },
      staleTime: 30 * 1000, // 30 seconds
      refetchInterval: 60 * 1000, // Refetch every minute
   });
}

// ============================================================================
// NOTIFICATION MESSAGE MANAGEMENT HOOKS
// ============================================================================

/**
 * Hook to get all notification messages
 */
export function useNotificationMessages() {
   return useQuery({
      queryKey: ["admin", "notification-messages"],
      queryFn: async () => {
         const result = await getNotificationMessages();
         if (!result.success) {
            throw new Error(
               result.message || "Failed to get notification messages"
            );
         }
         return result.messages!;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to create notification message
 */
export function useCreateNotificationMessage() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async (data: CreateNotificationMessageData) => {
         const result = await createNotificationMessageAction(data);
         if (!result.success) {
            throw new Error(
               result.error || "Failed to create notification message"
            );
         }
         return result.message!;
      },
      onSuccess: () => {
         // Invalidate notification messages queries
         queryClient.invalidateQueries({
            queryKey: ["admin", "notification-messages"],
         });
      },
   });
}

// ============================================================================
// CARD MANAGEMENT HOOKS
// ============================================================================

/**
 * Hook to get all cards for admin management
 */
export function useCards(options: {
   page?: number;
   limit?: number;
   status?: "active" | "inactive" | "blocked" | "expired";
   cardTier?: "standard" | "gold" | "platinum" | "black";
   userId?: string;
}) {
   return useQuery({
      queryKey: ["admin", "cards", options],
      queryFn: () => getCards(options),
      staleTime: 2 * 60 * 1000, // 2 minutes
   });
}
