"use client";

import { AdminSidebar } from "@/components/ui/admin-sidebar";
import { BalanceDisplay } from "@/components/ui/balance-display";
import {
   NotificationDropdown,
   type Notification,
} from "@/components/ui/notification";
import {
   SidebarInset,
   SidebarProvider,
   SidebarTrigger,
} from "@/components/ui/sidebar";
import { useAuth } from "@/hooks";
import {
   convertToNotificationInterface,
   useMarkAllNotificationsAsRead,
   useMarkNotificationAsRead,
   useUserNotifications,
} from "@/hooks/use-user-notifications";
import { canAccessAdminRoutes } from "@/lib/utils/rbac";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function AdminLayout({
   children,
}: {
   children: React.ReactNode;
}) {
   const router = useRouter();
   const { isAuthenticated, isPinVerified, isHydrated, user } = useAuth();

   // Get admin notifications from database - MUST be called before any conditional returns
   const { data: notificationsData } = useUserNotifications({
      userId: user?.id,
      page: 1,
      limit: 10,
   });

   const markAsReadMutation = useMarkNotificationAsRead();
   const markAllAsReadMutation = useMarkAllNotificationsAsRead();

   console.log("User: ", user);

   // Redirect if not authenticated, PIN not verified, or not admin
   useEffect(() => {
      // Only redirect after hydration to prevent SSR/client mismatch
      if (!isHydrated) return;

      if (!isAuthenticated) {
         router.replace("/login");
      } else if (!isPinVerified) {
         router.replace("/pin-verification");
      } else if (!canAccessAdminRoutes(user)) {
         router.replace("/dashboard"); // Redirect non-admins to regular dashboard
      }
   }, [isAuthenticated, isPinVerified, isHydrated, user, router]);

   // Show loading while hydrating to prevent flash
   if (!isHydrated) {
      return (
         <div className="flex items-center justify-center min-h-screen bg-background">
            <div className="flex flex-col items-center gap-4">
               <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
               <p className="text-sm text-muted-foreground">Loading...</p>
            </div>
         </div>
      );
   }

   // Show loading while checking authentication and admin access
   if (!isAuthenticated || !isPinVerified || !canAccessAdminRoutes(user)) {
      return (
         <div className="flex items-center justify-center min-h-screen bg-background">
            <div className="flex flex-col items-center gap-4">
               <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
               <p className="text-sm text-muted-foreground">
                  Authenticating...
               </p>
            </div>
         </div>
      );
   }

   // Convert to notification interface for UI components
   const notifications: Notification[] = notificationsData?.success
      ? convertToNotificationInterface(notificationsData.notifications || [])
      : [];

   const handleMarkNotificationAsRead = (id: string) => {
      markAsReadMutation.mutate(id);
   };

   const handleMarkAllNotificationsAsRead = () => {
      if (user?.id) {
         markAllAsReadMutation.mutate(user.id);
      }
   };

   const handleViewAllNotifications = () => {
      router.push("/admin/notifications");
   };

   return (
      <SidebarProvider>
         <div className="flex min-h-screen w-full bg-background">
            {/* Admin Sidebar */}
            <AdminSidebar />

            {/* Main Content Area */}
            <SidebarInset className="flex-1">
               {/* Header */}
               <header className="bg-card border-b border-border sticky top-0 z-40">
                  <div className="flex h-16 items-center justify-between px-4">
                     {/* Left side - Sidebar trigger */}
                     <div className="flex items-center gap-4">
                        <SidebarTrigger className="h-8 w-8" />
                        <div className="flex items-center gap-2">
                           <div className="h-2 w-2 bg-red-500 rounded-full animate-pulse"></div>
                           <span className="text-sm font-medium text-muted-foreground">
                              Admin Panel
                           </span>
                        </div>
                     </div>

                     {/* Right side - Balance, Notifications */}
                     <div className="flex items-center gap-4">
                        <BalanceDisplay />
                        <NotificationDropdown
                           notifications={notifications}
                           onMarkAsRead={handleMarkNotificationAsRead}
                           onMarkAllAsRead={handleMarkAllNotificationsAsRead}
                           onViewAll={handleViewAllNotifications}
                        />
                     </div>
                  </div>
               </header>

               {/* Main Content */}
               <main className="flex-1">{children}</main>
            </SidebarInset>
         </div>
      </SidebarProvider>
   );
}
