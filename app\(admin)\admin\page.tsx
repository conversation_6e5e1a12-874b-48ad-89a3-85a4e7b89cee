"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TransactionStatusBadge } from "@/components/ui/transaction-status-badge";
import { useAuth } from "@/hooks";
import { useTransactions } from "@/hooks/use-admin";
import { getAdminDashboardStatsAction } from "@/lib/actions";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import {
   AlertTriangle,
   CheckCircle,
   Clock,
   DollarSign,
   TrendingUp,
   Users,
   XCircle,
} from "lucide-react";
import { useRouter } from "next/navigation";

export default function AdminDashboard() {
   const { user } = useAuth();
   const router = useRouter();

   // Get real dashboard statistics from database
   const {
      data: statsData,
      isLoading: isLoadingStats,
      error: statsError,
   } = useQuery({
      queryKey: ["admin", "dashboard-stats"],
      queryFn: () => getAdminDashboardStatsAction(),
      staleTime: 60 * 1000, // 1 minute
      refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
   });

   // Get pending transactions count for urgent actions
   // const { data: pendingCount } = usePendingTransactions();

   // Get actual pending transactions for the urgent actions widget
   // const { data: pendingTransactionsData, isLoading: isLoadingPending } =
   const { data: pendingTransactionsData } = useTransactions({
      status: "pending",
      page: 1,
      limit: 5, // Only get first 5 for the urgent actions widget
   });

   const dashboardStats = (
      statsData?.success
         ? statsData.stats
         : {
              totalUsers: 0,
              newUsersToday: 0,
              totalTransactions: 0,
              pendingTransactions: 0,
              totalBalance: 0,
              failedTransactions: 0,
              completedTransactions: 0,
              unverifiedUsers: 0,
              totalAccounts: 0,
              activeAccounts: 0,
              totalCards: 0,
              pendingCardApplications: 0,
           }
   )!;

   // Get real pending transactions data
   const recentPendingTransactions =
      pendingTransactionsData?.transactions || [];

   const handleApproveTransaction = (transactionId: string) => {
      // In real app, this would call the API to approve the transaction
      console.log("Approve transaction:", transactionId);
      // Show success toast
   };

   const handleRejectTransaction = (transactionId: string) => {
      // In real app, this would call the API to reject the transaction
      console.log("Reject transaction:", transactionId);
      // Show success toast
   };

   // Show loading state
   if (isLoadingStats) {
      return (
         <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
               <h1 className="text-3xl font-bold text-foreground mb-2">
                  Welcome back, {user?.firstName}!
               </h1>
               <p className="text-muted-foreground">
                  Loading dashboard statistics...
               </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
               {[1, 2, 3, 4].map((i) => (
                  <Card key={i}>
                     <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <div className="animate-pulse">
                           <div className="h-4 bg-muted rounded w-24 mb-2"></div>
                           <div className="h-8 bg-muted rounded w-16"></div>
                        </div>
                     </CardHeader>
                  </Card>
               ))}
            </div>
         </div>
      );
   }

   // Show error state
   if (statsError) {
      return (
         <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
               <h1 className="text-3xl font-bold text-foreground mb-2">
                  Welcome back, {user?.firstName}!
               </h1>
               <p className="text-destructive">
                  Failed to load dashboard statistics. Please try again.
               </p>
            </div>
         </div>
      );
   }

   return (
      <div className="container mx-auto px-4 py-8">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
         >
            {/* Welcome Section */}
            <div className="mb-8">
               <h1 className="text-3xl font-bold text-foreground mb-2">
                  Welcome back, {user?.firstName}!
               </h1>
               <p className="text-muted-foreground">
                  Here&apos;s an overview of your admin dashboard and system
                  status.
               </p>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
               <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                     <CardTitle className="text-sm font-medium">
                        Total Users
                     </CardTitle>
                     <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                     <div className="text-2xl font-bold">
                        {dashboardStats.totalUsers.toLocaleString()}
                     </div>
                     <p className="text-xs text-muted-foreground">
                        +{dashboardStats.newUsersToday} new today
                     </p>
                  </CardContent>
               </Card>

               <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                     <CardTitle className="text-sm font-medium">
                        Pending Transactions
                     </CardTitle>
                     <Clock className="h-4 w-4 text-yellow-600" />
                  </CardHeader>
                  <CardContent>
                     <div className="text-2xl font-bold text-yellow-600">
                        {dashboardStats.pendingTransactions}
                     </div>
                     <p className="text-xs text-muted-foreground">
                        Require approval
                     </p>
                  </CardContent>
               </Card>

               <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                     <CardTitle className="text-sm font-medium">
                        Total Balance
                     </CardTitle>
                     <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                     <div className="text-2xl font-bold">
                        $
                        {dashboardStats.totalBalance.toLocaleString("en-US", {
                           minimumFractionDigits: 2,
                        })}
                     </div>
                     <p className="text-xs text-muted-foreground">
                        Across all accounts
                     </p>
                  </CardContent>
               </Card>

               <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                     <CardTitle className="text-sm font-medium">
                        Unverified Users
                     </CardTitle>
                     <AlertTriangle className="h-4 w-4 text-orange-600" />
                  </CardHeader>
                  <CardContent>
                     <div className="text-2xl font-bold text-orange-600">
                        {dashboardStats.unverifiedUsers}
                     </div>
                     <p className="text-xs text-muted-foreground">
                        Need verification
                     </p>
                  </CardContent>
               </Card>
            </div>

            {/* Urgent Actions Widget */}
            <Card className="mb-8 border-warning/20 bg-warning/5">
               <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-warning">
                     <AlertTriangle className="h-5 w-5" />
                     Urgent Actions Required
                  </CardTitle>
               </CardHeader>
               <CardContent>
                  <div className="space-y-4">
                     {/* Pending Transactions */}
                     {recentPendingTransactions.length > 0 && (
                        <div>
                           <div className="flex items-center justify-between mb-3">
                              <h4 className="font-medium flex items-center gap-2">
                                 <Clock className="h-4 w-4" />
                                 Pending Transactions (
                                 {recentPendingTransactions.length})
                              </h4>
                              <Button
                                 variant="outline"
                                 size="sm"
                                 onClick={() =>
                                    router.push("/admin/transactions/pending")
                                 }
                              >
                                 View All
                              </Button>
                           </div>
                           <div className="space-y-2">
                              {recentPendingTransactions
                                 .slice(0, 3)
                                 .map((transaction) => (
                                    <div
                                       key={transaction.id}
                                       className="flex items-center justify-between p-3 bg-background rounded-lg border cursor-pointer hover:bg-muted/50 transition-colors"
                                       onClick={() =>
                                          router.push(
                                             "/admin/transactions/pending"
                                          )
                                       }
                                    >
                                       <div className="flex items-center gap-3">
                                          <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-warning/10">
                                             <Clock className="w-4 h-4 text-warning" />
                                          </div>
                                          <div>
                                             <p className="font-medium text-sm">
                                                {transaction.description}
                                             </p>
                                             <p className="text-xs text-muted-foreground">
                                                ${transaction.amount.toFixed(2)}{" "}
                                                • {transaction.type}
                                             </p>
                                          </div>
                                       </div>
                                       <div className="text-right">
                                          <TransactionStatusBadge
                                             status={transaction.status}
                                          />
                                       </div>
                                    </div>
                                 ))}
                           </div>
                        </div>
                     )}

                     {/* Unverified Users */}
                     {dashboardStats.unverifiedUsers > 0 && (
                        <div>
                           <div className="flex items-center justify-between mb-3">
                              <h4 className="font-medium flex items-center gap-2">
                                 <Users className="h-4 w-4" />
                                 Unverified Users (
                                 {dashboardStats.unverifiedUsers})
                              </h4>
                              <Button
                                 variant="outline"
                                 size="sm"
                                 onClick={() => router.push("/admin/users")}
                              >
                                 Review
                              </Button>
                           </div>
                           <p className="text-sm text-muted-foreground">
                              {dashboardStats.unverifiedUsers} users need
                              verification review
                           </p>
                        </div>
                     )}

                     {/* No urgent actions */}
                     {recentPendingTransactions.length === 0 &&
                        dashboardStats.unverifiedUsers === 0 && (
                           <div className="text-center py-4">
                              <CheckCircle className="h-8 w-8 text-success mx-auto mb-2" />
                              <p className="text-sm text-muted-foreground">
                                 No urgent actions required at this time
                              </p>
                           </div>
                        )}
                  </div>
               </CardContent>
            </Card>

            {/* Transaction Status Overview */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
               <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                     <CardTitle className="text-sm font-medium">
                        Completed
                     </CardTitle>
                     <CheckCircle className="h-4 w-4 text-green-600" />
                  </CardHeader>
                  <CardContent>
                     <div className="text-2xl font-bold text-green-600">
                        {dashboardStats.completedTransactions.toLocaleString()}
                     </div>
                  </CardContent>
               </Card>

               <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                     <CardTitle className="text-sm font-medium">
                        Pending
                     </CardTitle>
                     <Clock className="h-4 w-4 text-yellow-600" />
                  </CardHeader>
                  <CardContent>
                     <div className="text-2xl font-bold text-yellow-600">
                        {dashboardStats.pendingTransactions}
                     </div>
                  </CardContent>
               </Card>

               <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                     <CardTitle className="text-sm font-medium">
                        Failed
                     </CardTitle>
                     <XCircle className="h-4 w-4 text-red-600" />
                  </CardHeader>
                  <CardContent>
                     <div className="text-2xl font-bold text-red-600">
                        {dashboardStats.failedTransactions}
                     </div>
                  </CardContent>
               </Card>
            </div>

            {/* Pending Transactions Card */}
            <Card>
               <CardHeader>
                  <div className="flex items-center justify-between">
                     <CardTitle className="text-xl font-semibold">
                        Pending Transactions
                     </CardTitle>
                     <Button
                        variant="outline"
                        onClick={() =>
                           router.push("/admin/transactions/pending")
                        }
                     >
                        View All
                     </Button>
                  </div>
               </CardHeader>
               <CardContent>
                  {recentPendingTransactions.length === 0 ? (
                     <div className="text-center py-8">
                        <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground">
                           No pending transactions
                        </p>
                     </div>
                  ) : (
                     <div className="space-y-4">
                        {recentPendingTransactions.map((transaction) => (
                           <div
                              key={transaction.id}
                              className="flex items-center justify-between p-4 rounded-lg border border-border"
                           >
                              <div className="flex items-center gap-4">
                                 <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-muted">
                                    <TrendingUp className="w-5 h-5 text-muted-foreground" />
                                 </div>
                                 <div>
                                    <div className="flex items-center gap-2">
                                       <p className="font-medium">
                                          Transaction #
                                          {transaction.transactionId}
                                       </p>
                                       <TransactionStatusBadge
                                          status={transaction.status}
                                       />
                                    </div>
                                    <p className="text-sm text-muted-foreground">
                                       {transaction.description}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                       {transaction.date.toLocaleString()}
                                    </p>
                                 </div>
                              </div>
                              <div className="flex items-center gap-3">
                                 <div className="text-right">
                                    <p
                                       className={`font-semibold ${
                                          transaction.type === "credit"
                                             ? "text-green-600"
                                             : "text-red-600"
                                       }`}
                                    >
                                       {transaction.type === "credit"
                                          ? "+"
                                          : "-"}
                                       $
                                       {transaction.amount.toLocaleString(
                                          "en-US",
                                          { minimumFractionDigits: 2 }
                                       )}
                                    </p>
                                 </div>
                                 <div className="flex gap-2">
                                    <Button
                                       size="sm"
                                       variant="outline"
                                       className="text-green-600 border-green-200 hover:bg-green-50"
                                       onClick={() =>
                                          handleApproveTransaction(
                                             transaction.id
                                          )
                                       }
                                    >
                                       <CheckCircle className="w-4 h-4 mr-1" />
                                       Approve
                                    </Button>
                                    <Button
                                       size="sm"
                                       variant="outline"
                                       className="text-red-600 border-red-200 hover:bg-red-50"
                                       onClick={() =>
                                          handleRejectTransaction(
                                             transaction.id
                                          )
                                       }
                                    >
                                       <XCircle className="w-4 h-4 mr-1" />
                                       Reject
                                    </Button>
                                 </div>
                              </div>
                           </div>
                        ))}
                     </div>
                  )}
               </CardContent>
            </Card>
         </motion.div>
      </div>
   );
}
