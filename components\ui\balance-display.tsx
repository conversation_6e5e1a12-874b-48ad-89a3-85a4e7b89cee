"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Eye, EyeOff } from "lucide-react";
import { useState } from "react";

export function BalanceDisplay() {
   const [showBalance, setShowBalance] = useState(true);

   const accountBalance = 12847.52;

   return (
      <div className="flex items-center gap-3 bg-card/50 px-4 py-2 border-x border-border/50">
         <div className="text-right">
            <p className="text-xs text-muted-foreground">Available Balance</p>
            <p className="text-lg font-bold text-foreground">
               {showBalance
                  ? `$${accountBalance.toLocaleString("en-US", {
                       minimumFractionDigits: 2,
                    })}`
                  : "• • • • • •"}
            </p>
         </div>
         <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowBalance(!showBalance)}
            className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
         >
            {showBalance ? (
               <EyeOff className="h-4 w-4" />
            ) : (
               <Eye className="h-4 w-4" />
            )}
         </Button>
      </div>
   );
}
