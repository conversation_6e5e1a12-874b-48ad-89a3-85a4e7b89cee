"use client";

import {
   Sidebar,
   <PERSON>bar<PERSON>ontent,
   SidebarFooter,
   SidebarGroup,
   SidebarGroupContent,
   SidebarGroupLabel,
   SidebarHeader,
   SidebarMenu,
   SidebarMenuButton,
   SidebarMenuItem,
   SidebarRail,
   useSidebar,
} from "@/components/ui/sidebar";
import { UserDropdown } from "@/components/ui/user-dropdown";
import { useAuth } from "@/hooks";
import {
   Clock,
   CreditCard,
   FileText,
   Home,
   MessageSquare,
   Plus,
   Settings,
   Shield,
   TrendingUp,
   User,
   Users,
   Wallet,
} from "lucide-react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";

interface NavigationItem {
   title: string;
   url: string;
   icon: React.ComponentType<{ className?: string }>;
   isActive?: boolean;
}

interface NavigationGroup {
   title: string;
   items: NavigationItem[];
}

export function AdminSidebar() {
   const pathname = usePathname();
   const router = useRouter();
   const { user: authUser, logout } = useAuth();
   const { isMobile, setOpenMobile } = useSidebar();

   const user = authUser || {
      id: "ADMIN-001",
      firstName: "Admin",
      lastName: "User",
      username: "admin",
      email: "<EMAIL>",
      phoneNumber: "+**********",
      country: "United States",
      accountType: "Administrator",
      role: "admin" as const,
      accountNumber: "ADMIN001",
      balance: 0,
      accountStatus: "Active",
      verificationStatus: "verified",
   };

   const userDropdownData = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      accountType: user.accountType,
      role: user.role,
      verificationStatus: user.verificationStatus,
   };

   const handleUserProfile = () => {
      router.push("/admin/profile");
   };

   const handleUserSupport = () => {
      router.push("/admin/support");
   };

   const handleLogout = () => {
      logout();
      router.push("/login");
   };

   const navigationGroups: NavigationGroup[] = [
      {
         title: "Dashboard",
         items: [
            {
               title: "Overview",
               url: "/admin",
               icon: Home,
               isActive: pathname === "/admin",
            },
         ],
      },
      {
         title: "User Management",
         items: [
            {
               title: "All Users",
               url: "/admin/users",
               icon: Users,
               isActive: pathname === "/admin/users",
            },
            {
               title: "User Verification",
               url: "/admin/users/verification",
               icon: Shield,
               isActive: pathname === "/admin/users/verification",
            },
            {
               title: "Role Management",
               url: "/admin/users/roles",
               icon: User,
               isActive: pathname === "/admin/users/roles",
            },
         ],
      },
      {
         title: "Financial Management",
         items: [
            {
               title: "Account Balances",
               url: "/admin/accounts",
               icon: Wallet,
               isActive: pathname === "/admin/accounts",
            },
            {
               title: "All Transactions",
               url: "/admin/transactions",
               icon: TrendingUp,
               isActive: pathname === "/admin/transactions",
            },
            {
               title: "Pending Transactions",
               url: "/admin/transactions/pending",
               icon: Clock,
               isActive: pathname === "/admin/transactions/pending",
            },
            {
               title: "New Transaction",
               url: "/admin/transactions/new",
               icon: Plus,
               isActive: pathname === "/admin/transactions/new",
            },
            {
               title: "Transaction Status",
               url: "/admin/transactions/status",
               icon: FileText,
               isActive: pathname === "/admin/transactions/status",
            },
         ],
      },
      {
         title: "Card Management",
         items: [
            {
               title: "Card Applications",
               url: "/admin/cards",
               icon: CreditCard,
               isActive: pathname === "/admin/cards",
            },
         ],
      },
      {
         title: "System Management",
         items: [
            {
               title: "Notifications",
               url: "/admin/notifications",
               icon: MessageSquare,
               isActive: pathname === "/admin/notifications",
            },
            {
               title: "System Settings",
               url: "/admin/settings",
               icon: Settings,
               isActive: pathname === "/admin/settings",
            },
         ],
      },
   ];

   return (
      <Sidebar
         variant="inset"
         className="border-r border-border bg-red-50/30 dark:bg-red-950/10"
         role="navigation"
         aria-label="Admin navigation"
      >
         <SidebarHeader className="border-b border-border bg-card">
            <div className="flex h-16 items-center px-4">
               <div className="flex items-center gap-2">
                  <Image
                     src="/images/logo.svg"
                     alt="Paramount Bank Logo"
                     width={100}
                     height={100}
                     className="h-8 w-auto"
                  />
                  <div className="flex items-center gap-1">
                     <div className="h-2 w-2 bg-red-500 rounded-full"></div>
                     <span className="text-xs font-medium text-red-600 dark:text-red-400">
                        ADMIN
                     </span>
                  </div>
               </div>
            </div>
         </SidebarHeader>

         <SidebarContent>
            {navigationGroups.map((group) => (
               <SidebarGroup key={group.title}>
                  <SidebarGroupLabel className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
                     {group.title}
                  </SidebarGroupLabel>
                  <SidebarGroupContent>
                     <SidebarMenu>
                        {group.items.map((item) => {
                           const IconComponent = item.icon;
                           return (
                              <SidebarMenuItem key={item.title}>
                                 <SidebarMenuButton
                                    isActive={item.isActive}
                                    onClick={() => {
                                       router.push(item.url);
                                       // Close sidebar on mobile after navigation
                                       if (isMobile) {
                                          setOpenMobile(false);
                                       }
                                    }}
                                    className="w-full justify-start"
                                    aria-label={`Navigate to ${item.title}`}
                                 >
                                    <IconComponent
                                       className="h-4 w-4"
                                       aria-hidden="true"
                                    />
                                    <span>{item.title}</span>
                                 </SidebarMenuButton>
                              </SidebarMenuItem>
                           );
                        })}
                     </SidebarMenu>
                  </SidebarGroupContent>
               </SidebarGroup>
            ))}
         </SidebarContent>

         <SidebarFooter className="border-t border-border bg-card">
            <div className="p-4">
               <UserDropdown
                  user={userDropdownData}
                  onProfile={handleUserProfile}
                  onSupport={handleUserSupport}
                  onLogout={handleLogout}
               />
            </div>
         </SidebarFooter>

         <SidebarRail />
      </Sidebar>
   );
}
