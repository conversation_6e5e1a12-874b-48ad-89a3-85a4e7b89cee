"use client";

import { AnimatePresence, motion } from "framer-motion";
import { CheckCircle, Shield, X } from "lucide-react";
import * as React from "react";
import { Button } from "./button";
import { PinInput } from "./input";

export interface TransactionPreviewModalProps {
   isOpen: boolean;
   onClose: () => void;
   onConfirm: (pin: string) => void;
   transaction: {
      type: string;
      recipientName: string;
      accountNumber: string;
      amount: string;
      description?: string;
      fee: string;
      total: string;
      processingTime: string;
   };
   isLoading?: boolean;
}

const TransactionPreviewModal = React.forwardRef<
   HTMLDivElement,
   TransactionPreviewModalProps
>(({ isOpen, onClose, onConfirm, transaction, isLoading = false }, ref) => {
   const [pin, setPin] = React.useState("");
   const [pinError, setPinError] = React.useState("");
   const [step, setStep] = React.useState<"preview" | "pin">("preview");

   React.useEffect(() => {
      if (isOpen) {
         setPin("");
         setPinError("");
         setStep("preview");
      }
   }, [isOpen]);

   const handleProceed = () => {
      setStep("pin");
   };

   const handleConfirmTransaction = () => {
      if (pin.length !== 4) {
         setPinError("Please enter your 4-digit PIN");
         return;
      }

      // For demo purposes, accept "1234" as correct PIN
      if (pin === "1234") {
         onConfirm(pin);
         setPin("");
         setPinError("");
         setStep("preview");
      } else {
         setPinError("Incorrect PIN. Please try again.");
         setPin("");
      }
   };

   const handleBack = () => {
      setStep("preview");
      setPin("");
      setPinError("");
   };

   return (
      <AnimatePresence>
         {isOpen && (
            <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
               <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="fixed inset-0 bg-black/50 backdrop-blur-sm"
                  onClick={onClose}
               />
               <motion.div
                  ref={ref}
                  initial={{ opacity: 0, scale: 0.95, y: 20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: 20 }}
                  transition={{ duration: 0.2 }}
                  className="relative w-full max-w-md bg-background border border-border rounded-lg shadow-lg"
               >
                  <div className="flex items-center justify-between p-6 border-b border-border">
                     <h2 className="text-lg font-semibold text-foreground">
                        {step === "preview"
                           ? "Transaction Preview"
                           : "Enter PIN"}
                     </h2>
                     <Button
                        variant="ghost"
                        size="icon"
                        onClick={onClose}
                        className="h-8 w-8"
                     >
                        <X className="h-4 w-4" />
                     </Button>
                  </div>

                  <div className="p-6">
                     {step === "preview" ? (
                        <div className="space-y-6">
                           <div className="text-center">
                              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-3">
                                 <CheckCircle className="w-6 h-6 text-primary" />
                              </div>
                              <p className="text-muted-foreground text-sm">
                                 Please review your transaction details
                              </p>
                           </div>

                           <div className="space-y-4">
                              <div className="flex justify-between">
                                 <span className="text-muted-foreground">
                                    Transaction Type:
                                 </span>
                                 <span className="font-medium">
                                    {transaction.type}
                                 </span>
                              </div>
                              <div className="flex justify-between">
                                 <span className="text-muted-foreground">
                                    Recipient:
                                 </span>
                                 <span className="font-medium">
                                    {transaction.recipientName}
                                 </span>
                              </div>
                              <div className="flex justify-between">
                                 <span className="text-muted-foreground">
                                    Account Number:
                                 </span>
                                 <span className="font-medium font-mono">
                                    ****{transaction.accountNumber.slice(-4)}
                                 </span>
                              </div>
                              <div className="flex justify-between">
                                 <span className="text-muted-foreground">
                                    Amount:
                                 </span>
                                 <span className="font-medium">
                                    ${transaction.amount}
                                 </span>
                              </div>
                              {transaction.description && (
                                 <div className="flex justify-between">
                                    <span className="text-muted-foreground">
                                       Description:
                                    </span>
                                    <span className="font-medium">
                                       {transaction.description}
                                    </span>
                                 </div>
                              )}
                              <div className="flex justify-between">
                                 <span className="text-muted-foreground">
                                    Fee:
                                 </span>
                                 <span className="font-medium text-success">
                                    {transaction.fee}
                                 </span>
                              </div>
                              <div className="flex justify-between border-t border-border pt-2">
                                 <span className="text-muted-foreground">
                                    Total:
                                 </span>
                                 <span className="font-semibold text-lg">
                                    ${transaction.total}
                                 </span>
                              </div>
                              <div className="flex justify-between">
                                 <span className="text-muted-foreground">
                                    Processing Time:
                                 </span>
                                 <span className="font-medium text-success">
                                    {transaction.processingTime}
                                 </span>
                              </div>
                           </div>

                           <div className="flex gap-3">
                              <Button
                                 variant="outline"
                                 onClick={onClose}
                                 className="flex-1"
                              >
                                 Cancel
                              </Button>
                              <Button
                                 variant="premium"
                                 onClick={handleProceed}
                                 className="flex-1"
                              >
                                 Proceed
                              </Button>
                           </div>
                        </div>
                     ) : (
                        <div className="space-y-6">
                           <div className="text-center">
                              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-3">
                                 <Shield className="w-6 h-6 text-primary" />
                              </div>
                              <p className="text-muted-foreground text-sm">
                                 Enter your 4-digit transaction PIN to confirm
                              </p>
                           </div>

                           <PinInput
                              length={4}
                              value={pin}
                              onChange={setPin}
                              error={pinError}
                              disabled={isLoading}
                           />

                           <div className="flex gap-3">
                              <Button
                                 variant="outline"
                                 onClick={handleBack}
                                 className="flex-1"
                              >
                                 Back
                              </Button>
                              <Button
                                 variant="premium"
                                 onClick={handleConfirmTransaction}
                                 className="flex-1"
                                 disabled={pin.length !== 4 || isLoading}
                              >
                                 {isLoading
                                    ? "Processing..."
                                    : "Confirm Transfer"}
                              </Button>
                           </div>

                           <div className="text-center">
                              <p className="text-xs text-muted-foreground">
                                 For demo purposes, use PIN: 1234
                              </p>
                           </div>
                        </div>
                     )}
                  </div>
               </motion.div>
            </div>
         )}
      </AnimatePresence>
   );
});

TransactionPreviewModal.displayName = "TransactionPreviewModal";

export { TransactionPreviewModal };
