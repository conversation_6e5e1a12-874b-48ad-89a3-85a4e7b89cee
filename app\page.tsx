"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import bankingHero from "@/public/images/hero.jpg";
import { motion } from "framer-motion";
import { ArrowRight, Award, Globe, Shield, Zap } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const features = [
   {
      icon: Shield,
      title: "Bank-Grade Security",
      description:
         "Your money and data are protected with military-grade encryption and advanced security protocols.",
   },
   {
      icon: Zap,
      title: "Lightning Fast",
      description:
         "Experience instant transfers and real-time updates on all your banking activities.",
   },
   {
      icon: Globe,
      title: "Global Access",
      description:
         "Access your account anywhere in the world with our comprehensive international network.",
   },
   {
      icon: Award,
      title: "Award Winning",
      description:
         "Recognized as the best digital banking platform for three consecutive years.",
   },
];

export default function Home() {
   return (
      <div className="min-h-screen flex flex-col relative overflow-hidden pt-10">
         {/* Background Image */}
         <div className="absolute inset-0">
            <Image
               src={bankingHero}
               fill
               className="object-cover"
               alt="Banking Hero"
            />
            <div className="absolute inset-0 bg-gradient-to-br from-primary/40 via-primary-hover/30 to-accent/20"></div>
            {/* <div className="absolute inset-0 bg-gradient-to-br from-primary/90 via-primary-hover/80 to-accent/70"></div> */}
         </div>

         {/* Hero Section */}
         <div className="relative z-10 container mx-auto px-4 py-16">
            <motion.div
               initial={{ opacity: 0, y: 30 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.8 }}
               className="text-center text-white"
            >
               <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.8 }}
                  className="text-5xl md:text-7xl font-bold mb-6"
               >
                  Paramount Bank
               </motion.h1>
               <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.8 }}
                  className="text-xl md:text-2xl mb-8 text-white/90 max-w-3xl mx-auto"
               >
                  Experience the future of banking with our cutting-edge digital
                  platform. Secure, fast, and designed for the modern world.
               </motion.p>

               <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.8 }}
                  className="flex flex-col sm:flex-row gap-4 justify-center"
               >
                  <Button
                     asChild
                     size="lg"
                     className="bg-white hover:bg-white/90"
                  >
                     <Link href="/signup">
                        Get Started
                        <ArrowRight className="ml-2 h-5 w-5" />
                     </Link>
                  </Button>
                  <Button
                     asChild
                     variant="outline"
                     size="lg"
                     className="border-none text-white hover:bg-white hover:text-primary"
                  >
                     <Link href="/login">Sign In</Link>
                  </Button>
               </motion.div>
            </motion.div>

            {/* Features Section */}
            <motion.div
               initial={{ opacity: 0, y: 50 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ delay: 0.8, duration: 0.8 }}
               className="mt-24 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            >
               {features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                     <motion.div
                        key={feature.title}
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 1 + index * 0.1, duration: 0.6 }}
                     >
                        <Card className="banking-card border-white/20 bg-white/10 backdrop-blur-lg text-white hover:bg-white/20 transition-all duration-300">
                           <CardContent className="p-6 text-center">
                              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                 <Icon className="h-8 w-8 text-white" />
                              </div>
                              <h3 className="text-lg font-semibold mb-2">
                                 {feature.title}
                              </h3>
                              <p className="text-white/80 text-sm">
                                 {feature.description}
                              </p>
                           </CardContent>
                        </Card>
                     </motion.div>
                  );
               })}
            </motion.div>
         </div>

         {/* CTA Section */}
         <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5, duration: 0.8 }}
            className="relative z-10 mt-auto bg-white/10 backdrop-blur-lg border-t border-white/20"
         >
            <div className="container mx-auto px-4 py-16 text-center text-white">
               <h2 className="text-3xl font-bold mb-4">
                  Ready to get started?
               </h2>
               <p className="text-white/80 mb-8 max-w-2xl mx-auto">
                  Join thousands of satisfied customers who have made the switch
                  to smarter banking.
               </p>
               <Button asChild size="lg" className="bg-white hover:bg-white/90">
                  <Link href="/register">
                     Open Your Account Today
                     <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
               </Button>
            </div>
         </motion.div>
      </div>
   );
}
