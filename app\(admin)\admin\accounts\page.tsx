"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";
import {
   AlertCircle,
   CreditCard,
   DollarSign,
   Minus,
   Plus,
   Search,
   User,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface UserAccount {
   id: string;
   userId: string;
   firstName: string;
   lastName: string;
   email: string;
   accountNumber: string;
   accountType: string;
   availableBalance: number;
   totalBalance: number;
   currency: string;
   accountStatus: "Active" | "Inactive" | "Suspended" | "Closed";
   verificationStatus: "verified" | "pending" | "unverified";
   dateOpened: Date;
}

export default function AdminAccounts() {
   const [searchTerm, setSearchTerm] = useState("");
   const [selectedUser, setSelectedUser] = useState<UserAccount | null>(null);
   const [adjustmentAmount, setAdjustmentAmount] = useState("");
   const [adjustmentReason, setAdjustmentReason] = useState("");
   const [isLoading] = useState(false);
   const [isAdjusting, setIsAdjusting] = useState(false);

   // Mock data - in real app, this would come from API
   const mockUsers: UserAccount[] = [
      {
         id: "acc-001",
         userId: "usr-001",
         firstName: "John",
         lastName: "Doe",
         email: "<EMAIL>",
         accountNumber: "**********",
         accountType: "Checking Account",
         availableBalance: 15420.5,
         totalBalance: 15420.5,
         currency: "USD",
         accountStatus: "Active",
         verificationStatus: "verified",
         dateOpened: new Date("2023-01-15"),
      },
      {
         id: "acc-002",
         userId: "usr-002",
         firstName: "Jane",
         lastName: "Smith",
         email: "<EMAIL>",
         accountNumber: "**********",
         accountType: "Savings Account",
         availableBalance: 8750.25,
         totalBalance: 8750.25,
         currency: "USD",
         accountStatus: "Active",
         verificationStatus: "verified",
         dateOpened: new Date("2023-03-22"),
      },
      {
         id: "acc-003",
         userId: "usr-003",
         firstName: "Mike",
         lastName: "Johnson",
         email: "<EMAIL>",
         accountNumber: "**********",
         accountType: "Premium Account",
         availableBalance: 2340.75,
         totalBalance: 2340.75,
         currency: "USD",
         accountStatus: "Active",
         verificationStatus: "pending",
         dateOpened: new Date("2023-11-08"),
      },
   ];

   const filteredUsers = mockUsers.filter(
      (user) =>
         user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
         user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
         user.accountNumber.includes(searchTerm)
   );

   const handleUserSelect = (userAccount: UserAccount) => {
      setSelectedUser(userAccount);
      setAdjustmentAmount("");
      setAdjustmentReason("");
   };

   const handleBalanceAdjustment = async (type: "credit" | "debit") => {
      if (!selectedUser || !adjustmentAmount || !adjustmentReason) {
         toast.error("Please fill in all required fields");
         return;
      }

      const amount = parseFloat(adjustmentAmount);
      if (isNaN(amount) || amount <= 0) {
         toast.error("Please enter a valid amount");
         return;
      }

      if (type === "debit" && amount > selectedUser.availableBalance) {
         toast.error("Insufficient balance for debit adjustment");
         return;
      }

      setIsAdjusting(true);

      try {
         // Simulate API call
         await new Promise((resolve) => setTimeout(resolve, 2000));

         const adjustment = type === "credit" ? amount : -amount;
         const newBalance = selectedUser.availableBalance + adjustment;

         // Update the selected user's balance
         setSelectedUser({
            ...selectedUser,
            availableBalance: newBalance,
            totalBalance: newBalance,
         });

         toast.success(
            `Balance ${
               type === "credit" ? "credited" : "debited"
            } successfully: $${amount.toFixed(2)}`
         );

         // Reset form
         setAdjustmentAmount("");
         setAdjustmentReason("");
      } catch (error) {
         console.error("Adjust balance error:", error);
         toast.error("Failed to adjust balance. Please try again.");
      } finally {
         setIsAdjusting(false);
      }
   };

   const getStatusBadge = (status: string) => {
      const variants = {
         Active: "default",
         Inactive: "secondary",
         Suspended: "destructive",
         Closed: "outline",
         verified: "default",
         pending: "secondary",
         unverified: "destructive",
      } as const;

      return (
         <Badge
            variant={variants[status as keyof typeof variants] || "outline"}
         >
            {status}
         </Badge>
      );
   };

   return (
      <div className="container mx-auto px-4 py-8">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
         >
            {/* Header */}
            <div className="mb-8">
               <h1 className="text-3xl font-bold text-foreground mb-2">
                  Account Balance Management
               </h1>
               <p className="text-muted-foreground">
                  Search for users and manage their account balances with proper
                  audit logging.
               </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
               {/* User Search and List */}
               <Card>
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <Search className="h-5 w-5" />
                        Search Users
                     </CardTitle>
                  </CardHeader>
                  <CardContent>
                     <div className="space-y-4">
                        <div>
                           <Label htmlFor="search">
                              Search by name, email, or account number
                           </Label>
                           <Input
                              id="search"
                              placeholder="Enter search term..."
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="mt-1"
                           />
                        </div>

                        <div className="space-y-3 max-h-96 overflow-y-auto">
                           {isLoading ? (
                              Array.from({ length: 3 }).map((_, index) => (
                                 <div
                                    key={index}
                                    className="p-4 border rounded-lg"
                                 >
                                    <div className="flex items-center gap-3">
                                       <Skeleton className="h-10 w-10 rounded-full" />
                                       <div className="space-y-2 flex-1">
                                          <Skeleton className="h-4 w-32" />
                                          <Skeleton className="h-3 w-48" />
                                       </div>
                                    </div>
                                 </div>
                              ))
                           ) : filteredUsers.length === 0 ? (
                              <div className="text-center py-8">
                                 <User className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                                 <p className="text-muted-foreground">
                                    {searchTerm
                                       ? "No users found"
                                       : "Enter a search term to find users"}
                                 </p>
                              </div>
                           ) : (
                              filteredUsers.map((userAccount) => (
                                 <div
                                    key={userAccount.id}
                                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                                       selectedUser?.id === userAccount.id
                                          ? "border-primary bg-primary/5"
                                          : "hover:bg-muted/50"
                                    }`}
                                    onClick={() =>
                                       handleUserSelect(userAccount)
                                    }
                                 >
                                    <div className="flex items-center gap-3">
                                       <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                                          <User className="w-5 h-5 text-primary" />
                                       </div>
                                       <div className="flex-1">
                                          <div className="flex items-center gap-2">
                                             <p className="font-medium">
                                                {userAccount.firstName}{" "}
                                                {userAccount.lastName}
                                             </p>
                                             {getStatusBadge(
                                                userAccount.verificationStatus
                                             )}
                                          </div>
                                          <p className="text-sm text-muted-foreground">
                                             {userAccount.email}
                                          </p>
                                          <p className="text-sm text-muted-foreground">
                                             Account:{" "}
                                             {userAccount.accountNumber}
                                          </p>
                                       </div>
                                       <div className="text-right">
                                          <p className="font-semibold">
                                             $
                                             {userAccount.availableBalance.toLocaleString(
                                                "en-US",
                                                { minimumFractionDigits: 2 }
                                             )}
                                          </p>
                                          {getStatusBadge(
                                             userAccount.accountStatus
                                          )}
                                       </div>
                                    </div>
                                 </div>
                              ))
                           )}
                        </div>
                     </div>
                  </CardContent>
               </Card>

               {/* Balance Adjustment */}
               <Card>
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <DollarSign className="h-5 w-5" />
                        Balance Adjustment
                     </CardTitle>
                  </CardHeader>
                  <CardContent>
                     {!selectedUser ? (
                        <div className="text-center py-12">
                           <CreditCard className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                           <p className="text-muted-foreground">
                              Select a user to manage their account balance
                           </p>
                        </div>
                     ) : (
                        <div className="space-y-6">
                           {/* Selected User Info */}
                           <div className="p-4 bg-muted/30 rounded-lg">
                              <div className="flex items-center gap-3 mb-3">
                                 <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                                    <User className="w-5 h-5 text-primary" />
                                 </div>
                                 <div>
                                    <p className="font-medium">
                                       {selectedUser.firstName}{" "}
                                       {selectedUser.lastName}
                                    </p>
                                    <p className="text-sm text-muted-foreground">
                                       {selectedUser.email}
                                    </p>
                                 </div>
                              </div>
                              <div className="grid grid-cols-2 gap-4 text-sm">
                                 <div>
                                    <p className="text-muted-foreground">
                                       Account Number
                                    </p>
                                    <p className="font-medium">
                                       {selectedUser.accountNumber}
                                    </p>
                                 </div>
                                 <div>
                                    <p className="text-muted-foreground">
                                       Account Type
                                    </p>
                                    <p className="font-medium">
                                       {selectedUser.accountType}
                                    </p>
                                 </div>
                                 <div>
                                    <p className="text-muted-foreground">
                                       Current Balance
                                    </p>
                                    <p className="font-semibold text-lg">
                                       $
                                       {selectedUser.availableBalance.toLocaleString(
                                          "en-US",
                                          { minimumFractionDigits: 2 }
                                       )}
                                    </p>
                                 </div>
                                 <div>
                                    <p className="text-muted-foreground">
                                       Status
                                    </p>
                                    <div className="flex gap-2">
                                       {getStatusBadge(
                                          selectedUser.accountStatus
                                       )}
                                       {getStatusBadge(
                                          selectedUser.verificationStatus
                                       )}
                                    </div>
                                 </div>
                              </div>
                           </div>

                           {/* Adjustment Form */}
                           <div className="space-y-4">
                              <div>
                                 <Label htmlFor="amount">
                                    Adjustment Amount
                                 </Label>
                                 <Input
                                    id="amount"
                                    type="number"
                                    placeholder="0.00"
                                    value={adjustmentAmount}
                                    onChange={(e) =>
                                       setAdjustmentAmount(e.target.value)
                                    }
                                    className="mt-1"
                                    min="0"
                                    step="0.01"
                                 />
                              </div>

                              <div>
                                 <Label htmlFor="reason">
                                    Reason for Adjustment
                                 </Label>
                                 <Input
                                    id="reason"
                                    placeholder="Enter reason for balance adjustment..."
                                    value={adjustmentReason}
                                    onChange={(e) =>
                                       setAdjustmentReason(e.target.value)
                                    }
                                    className="mt-1"
                                 />
                              </div>

                              <div className="flex gap-3">
                                 <Button
                                    onClick={() =>
                                       handleBalanceAdjustment("credit")
                                    }
                                    disabled={
                                       isAdjusting ||
                                       !adjustmentAmount ||
                                       !adjustmentReason
                                    }
                                    className="flex-1 bg-green-600 hover:bg-green-700"
                                 >
                                    <Plus className="w-4 h-4 mr-2" />
                                    Credit Balance
                                 </Button>
                                 <Button
                                    onClick={() =>
                                       handleBalanceAdjustment("debit")
                                    }
                                    disabled={
                                       isAdjusting ||
                                       !adjustmentAmount ||
                                       !adjustmentReason
                                    }
                                    variant="destructive"
                                    className="flex-1"
                                 >
                                    <Minus className="w-4 h-4 mr-2" />
                                    Debit Balance
                                 </Button>
                              </div>

                              {selectedUser.verificationStatus !==
                                 "verified" && (
                                 <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <AlertCircle className="w-4 h-4 text-yellow-600" />
                                    <p className="text-sm text-yellow-800">
                                       This user is not verified. Balance
                                       adjustments may be restricted.
                                    </p>
                                 </div>
                              )}
                           </div>
                        </div>
                     )}
                  </CardContent>
               </Card>
            </div>
         </motion.div>
      </div>
   );
}
